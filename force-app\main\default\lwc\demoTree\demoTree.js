import { LightningElement, wire, api } from 'lwc';
import { gql, graphql } from "lightning/uiGraphQLApi";

export default class DemoTree extends LightningElement {
    accounts = undefined;
    // Errors from the wire adapter
    errors = undefined;
    // Columns for datatable
    
    
    @api recordId;

//     @wire(graphql, {
//         query: gql`
//       query AccountWithName($recordId: ID) {
//   uiapi {
//     query {
//       Account( where: { Id: { eq: $recordId } }) {
//         edges {
//           node {
//             Id
//             Name {
//               value
//             }
//             Phone {
//               value
//             }
//             PartnerJobs__r {
//               edges {
//                 node {
//                   Name {
//                     value
//                   }
//                   Support_Worker_Name__c {
//                     value
//                   }
//                 }
//               }
//             }
//             Timesheets__r {
//               edges {
//                 node {
//                   Name {
//                     value
//                   }
//                   Id
//                 }
//               }
//             }
//           }
//         }
//       }
//       Timesheet__c(where: { Partner__c: { eq: $recordId } }) {
//         edges {
//           node {
//             Id
//             Name {
//               value
//             }
//             Time_Period__r {
//               Name {
//                 value
//               }
//             }
//             Timesheet_Entries__r {
//               edges {
//                 node {
//                   Id
//                   Date__c{
//                     displayValue
//                   }
//                   Shift_1__c{
//                     value
//                   }
//                 }
//               }
//             }
//           }
//         }
//       }
//     }
//   }
// }
//     `,
//         variables: "$queryData"
//     })
//     gqlQuery({ data, errors }) {
//         if (data) {
//             console.log('returned data :' + JSON.stringify(data));
//             this.accounts = data.uiapi.query.Account.edges.map((acc) => ({
//               name: acc.node.Id,
//               label: acc.node.Name.value,
//               items: acc.node.PartnerJobs__r?.edges?.map((j) => ({
//                   name: `${acc.node.Id}_${j.node.Id}`,
//                   label: j.node.Support_Worker_Name__c?.value,
//                   items: data.uiapi.query.Timesheet__c.edges
//                       .filter(t => t.node.PartnerJob__c?.value === j.node.Id)
//                       .map((t) => ({
//                           name: `${acc.node.Id}_${j.node.Id}_${t.node.Id}`,
//                           label: `${t.node.Name.value} - ${t.node.Time_Period__r?.Name?.value}` ,
//                           items: t.node.Timesheet_Entries__r?.edges?.map((e) => ({
//                               name: `${acc.node.Id}_${j.node.Id}_${t.node.Id}_${e.node.Id}`,
//                               label: `${e.node.Date__c?.displayValue} -  ${e.node.Shift_1__c?.value}`,
//                           }))
//                       }))
//               }))
//           }));
          
//           // Filter out any levels with empty _children arrays
//           const filterEmptyChildren = (items) => {
//               return items.map(item => {
//                   if (item._children && item._children.length > 0) {
//                       item._children = filterEmptyChildren(item._children);
//                   } else {
//                       delete item._children;
//                   }
//                   return item;
//               }).filter(item => !item._children || item._children.length > 0);
//           };
          
//           this.accounts = filterEmptyChildren(this.accounts);
        
//             console.log('accounts ' + JSON.stringify(this.accounts));
//         }
//         this.errors = errors;
//     }
//     get queryData() {
//         return {
//             recordId: this.recordId,
//         }
//     }
}