public with sharing class FileUploadCtrl {
    
    @AuraEnabled(cacheable=false)
    public static List<ContentDocumentLink> fetchFiles (string recordId){
        try {
            return [Select 
                        LinkedEntityId, 
                        ContentDocument.createddate, 
                        ContentDocument.Title, 
                        ContentDocument.FileType,  
                        ContentDocument.ContentSize 
                    from ContentDocumentLink 
                        where LinkedEntityId = :recordId];
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}