@isTest
public class TestRestrictContactByName {
    @isTest static void testInvalidNameContact(){
        
        Contact con = new Contact(FirstName='Test', LastName='INVALIDNAME');
        insert con;
        
        // Perform Test
        Test.startTest();
        Database.SaveResult result = Database.insert(con);
        System.assert(!result.isSuccess());
        System.assert(result.getErrors().size()>0);
        System.assertEquals('The Last Name "'+con.LastName+'" is not allowed for DML', result.getErrors()[0].getMessage());
        Test.stopTest();
    }
    
    @isTest static void testValidNameContact(){
        
        Contact con = new Contact(FirstName='Test', LastName='Test');
        insert con;
        
        // Perform Test
        Test.startTest();
        Database.SaveResult result = Database.insert(con);
        System.assert(result.isSuccess());
        System.assert(result.getErrors().size()<= 0);
        Test.stopTest();
    }
    
}