import { LightningElement, wire, api } from "lwc";
//import { gql, graphql } from "lightning/uiGraphQLApi";
import getPartnerHierarchy from "@salesforce/apex/JobController.getPartnerHierarchy";

const columns = [
  {
    type: "text",
    fieldName: "Partner",
    label: "Partner Name",
    iconName: "standard:account"
  },

  {
    type: "text",
    fieldName: "SupportWorker",
    label: "Support Worker Name"
  },
  {
    type: "text",
    fieldName: "Timesheet",
    label: "Timesheet"
  },
  {
    type: "text",
    fieldName: "Status",
    label: "Status"
  },
  {
    type: "text",
    fieldName: "Period",
    label: "Period"
  },
  {
    type: "text",
    fieldName: "Date",
    label: "Date"
  },
  {
    type: "text",
    fieldName: "Shift1",
    label: "Shift1"
  },
  {
    type: "text",
    fieldName: "Shift2",
    label: "Shift2"
  },
  {
    type: "text",
    fieldName: "Hours",
    label: "Hours"
  },
  {
    type: "text",
    fieldName: "TsHours",
    label: "Total Hours"
  }
];
export default class DemoTreeGrid extends LightningElement {
  // Array of accounts to display
  accounts = undefined;
  // Errors from the wire adapter
  errors = undefined;
  // Columns for datatable
  columns = columns;

  @api recordId;

  @wire(getPartnerHierarchy, { recordId: "$recordId" })
  wiredAccounts({ data, errors }) {
    if (data) {
      console.log("server data", data);
      //let parseData = JSON.parse(data);
      const d = data.map((acc) => {
        const partnerChildren = acc?.PartnerJobs__r?.map((s) => {
          const supportWorkerChildren = s?.Timesheets__r?.map((t) => {
            const timesheetChildren = t.Timesheet_Entries__r?.map((e) => {
              return {
                Date: e.Date__c,
                Shift1: e.Shift_1__c,
                Shift2: e.Shift_2__c,
                Hours: e.Total_Hours__c
              };
            }).filter(Boolean);

            return {
              Id: t.Id,
              Timesheet: t.Name,
              Status: t.Status__c,
              Period: t.Time_Period__r.Name,
              TsHours: t.Total_Hours__c,
              ...(timesheetChildren?.length && { _children: timesheetChildren })
            };
          }).filter(Boolean);

          return {
            Id: s.Id,
            SupportWorker: s?.Support_Worker__r?.Name,
            ...(supportWorkerChildren?.length && {
              _children: supportWorkerChildren
            })
          };
        }).filter(Boolean);

        return {
          Id: acc.Id,
          Partner: acc.Name,
          ...(partnerChildren?.length && { _children: partnerChildren })
        };
      });
      this.accounts = d;
    }
  }

  // console.log('apex data', data);
  //       this.accounts = data;
  //       this.errors = errors;
  //   }

  //     @wire(graphql, {
  //         query: gql`
  //       query AccountWithName($recordId: ID) {
  //   uiapi {
  //     query {
  //       Account( where: { Id: { eq: $recordId } }) {
  //         edges {
  //           node {
  //             Id
  //             Name {
  //               value
  //             }
  //             Phone {
  //               value
  //             }
  //             PartnerJobs__r {
  //               edges {
  //                 node {
  //                   Name {
  //                     value
  //                   }
  //                   Support_Worker_Name__c {
  //                     value
  //                   }
  //                 }
  //               }
  //             }
  //             Timesheets__c: Timesheets__r {
  //               edges {
  //                 node {
  //                   Name {
  //                     value
  //                   }
  //                   Id
  //                 }
  //               }
  //             }
  //           }
  //         }
  //       }
  //       Timesheet__c(where: { Partner__c: { eq: $recordId } }) {
  //         edges {
  //           node {
  //             Id
  //             Name {
  //               value
  //             }
  //             Time_Period__r {
  //               Name {
  //                 value
  //               }
  //             }
  //             Timesheet_Entries__r {
  //               edges {
  //                 node {
  //                   Id
  //                   Date__c{
  //                     displayValue
  //                   }
  //                   Shift_1__c{
  //                     value
  //                   }
  //                 }
  //               }
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }
  // }
  //     `,
  //         variables: "$queryData"
  //     })
  //     gqlQuery({ data, errors }) {
  //         if (data) {

  //             console.log('returned data :' + JSON.stringify(data));
  //             this.accounts = data.uiapi.query.Account.edges.map((acc) => ({
  //                 Id: acc.node.Id,
  //                 Name: acc.node.Name.value,
  //                 SupportWorker: null,
  //                 Job:null,
  //                 Timesheet: null,
  //                 Period: null,
  //                 Date: null,
  //                 Shift1: null,
  //                 _children: acc.node.PartnerJobs__r.edges.map((j) => ({
  //                   Id: j.node.Id,
  //                   Name: acc.node.Name.value,
  //                   Job: j.node.Name.value,
  //                   SupportWorker: j.node.Support_Worker_Name__c.value,
  //                   Timesheet: null,
  //                   Period: null,
  //                   Date: null,
  //                   Shift1: null,
  //                   _children: data.uiapi.query.Timesheet__c.edges.map((t) => ({
  //                     Id: t.node.Id,
  //                     Name: acc.node.Name.value,
  //                     SupportWorker: j.node.Support_Worker_Name__c.value,
  //                     Job: j.node.Name.value,
  //                     Timesheet: t.node.Name.value,
  //                     Period: t.node.Time_Period__r.Name.value,
  //                     Date: null,
  //                     Shift1: null,
  //                     _children: t.node.Timesheet_Entries__r.edges.map((e) => ({
  //                       Id: e.node.Id,
  //                       Name: acc.node.Name.value,
  //                       SupportWorker: j.node.Support_Worker_Name__c.value,
  //                       Job: j.node.Name.value,
  //                       Timesheet: t.node.Name.value,
  //                       Period: t.node.Time_Period__r.Name.value,
  //                       Date: e.node.Date__c.displayValue,
  //                       Shift1: e.node.Shift_1__c.value
  //                         }))
  //                     }))
  //                 })),
  //             }));

  //             console.log('accounts ' + JSON.stringify(this.accounts));
  //         }
  //         this.errors = errors;
  //     }
  // get queryData() {
  //     return {
  //         recordId: this.recordId,
  //     }
  // }
}