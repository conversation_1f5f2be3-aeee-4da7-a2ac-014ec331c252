/* activityTimeline.css */

.activity-timeline-container {
    font-family: var(--lwc-fontFamily, 'Salesforce Sans', Arial, sans-serif);
    font-size: var(--lwc-fontSize3, 0.8125rem);
    /* Matches SLDS text-body-small */
    color: var(--lwc-colorTextDefault, #181818);
    border: 1px solid var(--lwc-colorBorder, #d8dde6);
    /* Optional: Add border around the whole component */
    border-radius: var(--lwc-borderRadiusMedium, 0.25rem);
    background-color: var(--lwc-colorBackground, #ffffff);
}

/* Tabs */
.timeline-tabs {
    display: flex;
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
    padding: 0 1rem;
    /* Match padding of filter bar */
}

.timeline-tab {
    padding: 0.75rem 1rem;
    cursor: pointer;
    color: var(--lwc-colorTextWeak, #54698d);
    /* SLDS text-color_weak */
    font-weight: var(--lwc-fontWeightLight, 300);
}

.timeline-tab.active {
    color: var(--lwc-colorTextBrand, #0176d3);
    /* SLDS text-color_brand */
    border-bottom: 2px solid var(--lwc-colorBorderBrand, #0176d3);
    /* SLDS border-color_brand */
    font-weight: var(--lwc-fontWeightRegular, 400);
    margin-bottom: -1px;
    /* Pull the border up to overlap the container border */
}

/* Filter Bar */
.timeline-filter-bar {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    /* Adjust padding to match screenshot */
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
    flex-wrap: wrap;
    /* Allow wrapping on smaller screens */
}

.timeline-icons {
    display: flex;
    margin-right: 1rem;
    /* Space between icons and filter text */
}

.timeline-icon-button {
    background: none;
    border: 1px solid transparent;
    padding: 0.25rem;
    /* Adjust padding */
    margin-right: 0.25rem;
    /* Space between icon buttons */
    cursor: pointer;
    display: flex;
    align-items: center;
    border-radius: var(--lwc-borderRadiusMedium, 0.25rem);
}

.timeline-icon-button:hover {
    background-color: var(--lwc-colorBackgroundRowHover, #f3f3f3);
    /* SLDS color-background-row-hover */
}

.timeline-icon-button.active {
    border-color: var(--lwc-colorBorder, #d8dde6);
    /* Add border for active state */
    background-color: var(--lwc-colorBackground, #ffffff);
}

.timeline-icon-button lightning-icon {
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
    /* SLDS text-color_weak for icons */
    display: inline-flex;
    /* Ensure icons align correctly */
    vertical-align: middle;
}

.timeline-icon-button lightning-icon:last-child {
    /* Style for the dropdown arrow */
    margin-left: 0.1rem;
    /* Small space between main icon and dropdown */
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
}


.timeline-filter-text {
    flex-grow: 1;
    /* Allows text to take available space */
    color: var(--lwc-colorTextWeak, #54698d);
    /* SLDS text-color_weak */
    margin-right: 1rem;
}

.timeline-actions a {
    color: var(--lwc-colorTextLink, #0176d3);
    /* SLDS text-color_link */
    text-decoration: none;
    margin: 0 0.25rem;
    /* Space around the bullet points */
}

.timeline-actions a:hover {
    text-decoration: underline;
}

.timeline-settings-button {
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    margin-left: 0.5rem;
    /* Space from actions */
    border-radius: var(--lwc-borderRadiusMedium, 0.25rem);
}

.timeline-settings-button:hover {
    background-color: var(--lwc-colorBackgroundRowHover, #f3f3f3);
}

.timeline-settings-button lightning-icon {
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
}


/* Section Header */
.timeline-section-header {
    background-color: var(--lwc-colorBackgroundAlt, #f3f3f3);
    /* SLDS color-background-alt */
    padding: 0.5rem 1rem;
    font-weight: var(--lwc-fontWeightBold, 700);
    display: flex;
    align-items: center;
    cursor: pointer;
}

.timeline-section-header lightning-icon {
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
}


/* Timeline Items Container */
.timeline-items-container {
    padding: 0 1rem;
    /* Padding on the sides */
}

/* Individual Timeline Item */
.timeline-item {
    display: flex;
    padding: 0.75rem 0;
    /* Vertical padding for each item */
}

/* Left Side */
.timeline-left {
    width: 2rem;
    /* Fixed width for the left column */
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    /* Needed for positioning the vertical line */
    padding-top: 0.2rem;
    /* Align icon better with content */
}

.timeline-left lightning-icon:first-child {
    /* Style for the expand arrow */
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
    margin-bottom: 0.25rem;
    /* Space between arrow and icon */
}

.timeline-icon-wrapper {
    width: 1.25rem;
    /* Match icon size */
    height: 1.25rem;
    /* Match icon size */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--lwc-colorBackgroundBrand, #0176d3);
    /* Blue background for task icon */
    border-radius: 50%;
    /* Make it round */
    position: relative;
    /* Needed for line positioning */
    z-index: 1;
    /* Ensure icon is above the line */
}

.timeline-icon-wrapper lightning-icon {
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconInverse, #ffffff);
    /* White icon color */
}


.timeline-vertical-line {
    position: absolute;
    top: 1.5rem;
    /* Start below the icon wrapper */
    bottom: -0.75rem;
    /* Extend to the bottom padding of the item */
    left: 50%;
    /* Center the line */
    width: 1px;
    background-color: var(--lwc-colorBorder, #d8dde6);
    /* SLDS border color */
    transform: translateX(-50%);
    /* Adjust for width */
    z-index: 0;
    /* Keep line behind the icon */
}


/* Right Side */
.timeline-right {
    flex-grow: 1;
    /* Takes remaining space */
    padding-left: 1rem;
    /* Space between left column and content */
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
    /* Separator line below content */
    padding-bottom: 0.75rem;
    /* Match item padding */
}

/* Remove border for the last item */
.timeline-item:last-child .timeline-right {
    border-bottom: none;
}


.timeline-item-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
    /* Space between header and description */
}

.timeline-item-header input[type="checkbox"] {
    margin-right: 0.5rem;
    /* Space after checkbox */
    /* Custom styling for checkbox if needed, but SLDS faux checkbox should work */
}

.timeline-item-subject {
    flex-grow: 1;
    /* Subject takes available space */
    color: var(--lwc-colorTextLink, #0176d3);
    /* SLDS text-color_link */
    text-decoration: none;
    font-weight: var(--lwc-fontWeightBold, 700);
    margin-right: 0.5rem;
    /* Space before due date */
}

.timeline-item-subject:hover {
    text-decoration: underline;
}

.timeline-item-duedate {
    color: var(--lwc-colorTextWeak, #54698d);
    /* SLDS text-color_weak */
    margin-right: 0.5rem;
    /* Space before dropdown */
}

.timeline-item-dropdown {
    background: none;
    border: 1px solid var(--lwc-colorBorder, #d8dde6);
    /* Border for dropdown button */
    padding: 0.1rem 0.2rem;
    /* Adjust padding */
    cursor: pointer;
    border-radius: var(--lwc-borderRadiusMedium, 0.25rem);
    display: flex;
    /* Center icon */
    align-items: center;
    justify-content: center;
}

.timeline-item-dropdown lightning-icon {
    --sds-c-icon-color-foreground: var(--lwc-colorTextIconUtility, #54698d);
}


.timeline-item-description {
    color: var(--lwc-colorTextDefault, #181818);
}

/* View More Button */
.timeline-view-more {
    text-align: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
}

.timeline-view-more .slds-button_neutral {
    /* SLDS neutral button styling should be sufficient */
}


/* No Past Activity Message */
.timeline-no-past-activity {
    /* SLDS utility classes handle styling */
}

/* Adjustments for the vertical line on the last item */
/* The isLast flag in JS combined with template if:false handles hiding the div */