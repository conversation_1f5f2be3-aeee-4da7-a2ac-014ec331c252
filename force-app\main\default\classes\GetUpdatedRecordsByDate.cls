@RestResource(urlMapping='/getUpdatedRecordsByDate/*')
global with sharing class GetUpdatedRecordsByDate {
    
    @HttpGet
    global static void doGet() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        
        //String sinceDateStr = req.requestURI.substring(req.requestURI.lastIndexOf('/')+1);
        String sinceDateStr = req.params.get('sinceDate');
        
        if (String.isBlank(sinceDateStr)) {
            res.statusCode = 400;
            res.responseBody = Blob.valueOf('{"error": "Missing or empty date parameter"}');
            return;
        }
        
        try {
            Date sinceDate = Date.valueOf(sinceDateStr);
            DateTime sinceDateTime = DateTime.newInstance(sinceDate.year(), sinceDate.month(), sinceDate.day());
            
            List<Account> accounts = [SELECT Id, Name, LastModifiedDate FROM Account WHERE LastModifiedDate >= :sinceDateTime];
            List<Contact> contacts = [SELECT Id, Name, LastModifiedDate FROM Contact WHERE LastModifiedDate >= :sinceDateTime];
            List<Lead> leads = [SELECT Id, Name, LastModifiedDate FROM Lead WHERE LastModifiedDate >= :sinceDateTime];
            
            Map<String, List<SObject>> result = new Map<String, List<SObject>>();
            result.put('Accounts', accounts);
            result.put('Contacts', contacts);
            result.put('Leads', leads);
            
            res.statusCode = 200;
            res.responseBody = Blob.valueOf(JSON.serialize(result));
        } catch (IllegalArgumentException e) {
            res.statusCode = 400;
            res.responseBody = Blob.valueOf('{"error": "Invalid date format. Please use yyyy-MM-dd."}');
        } catch (Exception e) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('{"error": "' + e.getMessage() + '"}');
        }
    }
}
