public class LeadProcessor implements Database.Batchable<sObject>, Database.Stateful {

    public Integer recordsProcessed = 0;
   
    // Start
    public Database.QueryLocator start (Database.BatchableContext bc){
        return Database.getQueryLocator('select Id, LeadSource from Lead');
    }
    // Execute
    public void execute(Database.BatchableContext bc, List<Lead> scope){
        //process leads
        for(Lead l : scope){
            l.LeadSource= 'Dreamforce';
            recordsProcessed+=1;
        }
        update scope;
    }
    // Finish
    public void finish(Database.BatchableContext bc){
        // Post processing operation
        System.debug(recordsProcessed +' Leads processed');
    }
}