// Use .soql files to store SOQL queries.
// You can execute queries in VS Code by selecting the
//     query text and running the command:
//     SFDX: Execute SOQL Query with Currently Selected Text

                            
//SELECT Id, Timesheet__r.Job__r.Name, Timesheet__r.Support_Worker__r.Name,  Timesheet__r.partner__r.Name from Timesheet_Entry__c where Timesheet__r.Support_worker__c != null LIMIT 10
//SELECT Id, Name, (SELECT id, Name, (SELECT Id, Name, (SELECT Id, Name, Date__c, Shift_1__c FROM Timesheet_entries__r)  FROM Timesheets__r)  FROM PartnerJobs__r) FROM Account WHERE Partner_Category__pc ='Direct Employ' LIMIT 10
SELECT Id, Partner__r.Name FROM Job__c
