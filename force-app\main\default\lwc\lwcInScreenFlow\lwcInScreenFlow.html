<template>
   <lightning-card icon-name="standard:account" variant="base">
     <div slot="title">
         Show Flow
     </div>
     <div slot="actions">
   
     </div>
     <div slot="footer">
   
     </div>
     <div class="slds-p-horizontal_medium">
       <!-- Card Body  -->
     
        Output: {flowOutput}
     
    <lightning-input 
      type="text"
      label="Comma separated string"
      value={inputVars}
      onblur={handleChange}
    ></lightning-input>
       <template if:true={show}>
        <lightning-flow data-name="flw" flow-api-name="DemoString_util" onstatuschange={handleStatusChange} flow-input-variables={inputVars} ></lightning-flow>
    
       </template>
     </div>
   </lightning-card> 
   
    
</template>