public class AddPrimaryContact implements Queueable {
    private Contact contact;
    private string state;
    public AddPrimaryContact(Contact con, string st  ){
        this.contact = con;
        this.state= st;
    }
    
    public void execute(QueueableContext context){
        List<Account> lstAccount= [select Id from Account where BillingState=:state limit 200];
        List<Contact> lstContact = new List<Contact>();
        for(Account a: lstAccount){
            Contact contactClone = contact.clone();
            contactClone.AccountId = a.id;
            lstContact.add(contactClone);
        }
        insert lstContact;
    }
}