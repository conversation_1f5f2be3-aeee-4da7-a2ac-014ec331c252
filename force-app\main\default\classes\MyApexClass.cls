public with sharing class MyApexClass { 
   @AuraEnabled(cacheable=true)
   public static List<String> getRecordTypes(){
    try {
        List<String> names = new List<String>();
        for(Lead acc: [Select Id, Name from Lead limit 20]){
            names.add(acc.Name);
        }
        System.debug('names are '+ names);
        return names;
    } catch (Exception e) {
        throw new AuraHandledException(e.getMessage());
    }
   }
   @AuraEnabled
   public static string getAccounts(){
    try {
        
    } catch (Exception e) {
        throw new AuraHandledException(e.getMessage());
    }
   }
}