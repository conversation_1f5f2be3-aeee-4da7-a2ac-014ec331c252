public with sharing class ShowJsonData {
  @AuraEnabled
  public static String Display(String data) {
    try {
      List<MyJson> m = (List<MyJson>) System.JSON.deserialize(
        data,
        List<MyJson>.class
      );
      List<Contact> lstCon = new List<Contact>();
      sobj
      for (MyJson obj : m) {
        Contact c = new Contact(
          FirstName = obj.Header,
          LastName = obj.Value,
          AccountId = '0012w00000Pr1AUAAZ'
        );
        lstCon.add(c);
      }
      if (lstCon.size() > 0) {
        insert lstCon;
      }
      System.debug(m);
      return  lstCon.size()+' records inserted';
    } catch (Exception e) {
      return e.getMessage();
    }
  }
}