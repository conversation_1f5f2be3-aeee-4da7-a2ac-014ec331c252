public class AnimalLocator {
    
    public static string getAnimalNameById(Integer id){
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        request.setEndpoint('https://th-apex-http-callout.herokuapp.com/animals/'+id);
        request.setMethod('GET');
        HttpResponse response = http.send(request);
        
        Map<String, Object> result = (Map<String, Object>) Json.deserializeUntyped(response.getBody());
        Map<String, Object> animal= (Map<String, Object>)result.get('animal');
        return string.valueOf(animal.get('name'));
        
    }
}