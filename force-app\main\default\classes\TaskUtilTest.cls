@isTest
private class TaskUtilTest {
    @isTest static void testTaskPriority(){
        String pri = TaskUtil.getTaskPriority('NY');
        System.assertEquals('Normal', pri);
    }
    
    @isTest static void testTaskHighPriority(){
        String pri = TaskUtil.getTaskPriority('CA');
        System.assertEquals('High', pri);
    }

    @isTest static void testTaskPriorityInvalid(){
        String pri = TaskUtil.getTaskPriority('');
        System.assertEquals(null, pri);
    }
}