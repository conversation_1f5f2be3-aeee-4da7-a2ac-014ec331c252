@isTest
public class DailyLeadProcessorTest {
    
    @isTest
    static void DailyLeadTest(){
        //Load Test Data
        List<Lead> leads = new List<Lead>();
        for(Integer i=0;i<500;i++){
            Lead l = new Lead(LastName='LastName'+i, Company='Company'+i);
            l.LeadSource = i < 250? null: 'Other';
            leads.add(l);
        }
        insert leads;
        
        //Perform Test
        Test.startTest();
        Id jobId= System.schedule('Lead processing', '0 0 0 ? * * *', new DailyLeadProcessor());
        Test.stopTest();
        
        
        //Check Result
        List<Lead> leadsUpdated = [select id from lead where LeadSource='Dreamforce'];
        System.assertEquals(200, leadsUpdated.size());
        
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, 
                          NextFireTime
                          FROM CronTrigger WHERE id = :jobId];        
        System.debug('Next job fire sceduled at '+ ct.NextFireTime );
    }
}