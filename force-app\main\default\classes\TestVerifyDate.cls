@isTest
private class TestVerifyDate {
    //test within 30 days
    @isTest static void testDateWithin30Days(){
        Date date1 = Date.newInstance(2020, 10, 20);
        Date date2= Date.newInstance(2020, 10, 29);
        Date dt = VerifyDate.CheckDates( date1, date2 );
        System.assertEquals(date2, dt);
    } 
    
    //test out of 30 days
    @isTest static void testOutOf30Days(){
        Date date1 = Date.newInstance(2020, 8, 20);
        Date date2= Date.newInstance(2020, 10, 29);
        Date dt = VerifyDate.CheckDates( date1, date2 );
        System.assertEquals(Date.newInstance(2020, 8, 31), dt);
    } 
    
}