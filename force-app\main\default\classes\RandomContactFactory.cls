public class RandomContactFactory {
	
    public static List<Contact> generateRandomContacts(integer numOfContacts, string lastName){
        List<Contact> conList = new List<Contact>();
        
        for(integer i=0;i< numOfContacts;i++){
            Contact c = new Contact(FirstName= 'FirstName '+i, LastName= lastName+' '+ i);
            conList.add(c);
        }
        
        return conList;
    }
}