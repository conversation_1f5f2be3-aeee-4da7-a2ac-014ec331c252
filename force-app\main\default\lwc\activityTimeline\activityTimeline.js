import { LightningElement, track } from 'lwc';
import timelineData from './timelineData.json'; // Import the dummy data

export default class ActivityTimeline extends LightningElement {
    @track timelineItems = [];
    @track hasPastActivity = false; // Set to true if you want to show past activity

    connectedCallback() {
        // Load dummy data and add an 'isLast' flag for CSS
        if (timelineData && timelineData.length > 0) {
            this.timelineItems = timelineData.map((item, index) => ({
                ...item,
                isLast: index === timelineData.length - 1
            }));
        } else {
            this.timelineItems = [];
        }
    }

    // Placeholder handlers for actions
    handleRefresh(event) {
        event.preventDefault();
        console.log('Refresh clicked');
        // Implement refresh logic here
    }

    handleExpandAll(event) {
        event.preventDefault();
        console.log('Expand All clicked');
        // Implement expand all logic here
    }

    handleViewAll(event) {
        event.preventDefault();
        console.log('View All clicked');
        // Implement view all logic here
    }

    handleViewMore() {
        console.log('View More clicked');
        // Implement view more logic here (e.g., load more items)
    }

    // Add other handlers as needed (e.g., for checkbox, dropdown)
}