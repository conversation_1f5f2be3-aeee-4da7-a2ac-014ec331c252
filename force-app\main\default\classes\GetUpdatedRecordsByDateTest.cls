@isTest
private class GetUpdatedRecordsByDateTest {
    
    @isTest
    static void testDoGetValidDate() {
        // Create test data
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        
        Contact con = new Contact(LastName = 'Test Contact', AccountId = acc.Id);
        insert con;
        
        Lead lead = new Lead(LastName = 'Test Lead', Company = 'Test Company');
        insert lead;
        
        Test.startTest();
        
        // Set up the request
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?sinceDate=2025-02-01';
        req.httpMethod = 'GET';
        RestContext.request = req;
        
        // Set up the response
        RestResponse res = new RestResponse();
        RestContext.response = res;
        
        // Call the method
        GetUpdatedRecordsByDate.doGet();
        
        // Verify the response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('"Accounts":['));
        System.assert(responseBody.contains('"Contacts":['));
        System.assert(responseBody.contains('"Leads":['));
        
        Test.stopTest();
    }
    
    @isTest
    static void testDoGetMissingDate() {
        Test.startTest();
        
        // Set up the request
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?';
        req.httpMethod = 'GET';
        RestContext.request = req;
        
        // Set up the response
        RestResponse res = new RestResponse();
        RestContext.response = res;
        
        // Call the method
        GetUpdatedRecordsByDate.doGet();
        
        // Verify the response
        System.assertEquals(400, res.statusCode);
        System.assert(res.responseBody.toString().contains('Missing or empty date parameter'));
        
        Test.stopTest();
    }
    
    @isTest
    static void testDoGetInvalidDateFormat() {
        Test.startTest();
        
        // Set up the request
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?sinceDate=invalid-date';
        req.httpMethod = 'GET';
        RestContext.request = req;
        
        // Set up the response
        RestResponse res = new RestResponse();
        RestContext.response = res;
        
        // Call the method
        GetUpdatedRecordsByDate.doGet();
        
        // Verify the response
        System.assertEquals(400, res.statusCode, 'Expected 400 Bad Request for invalid date format');
        System.assert(res.responseBody.toString().contains('Invalid date format. Please use yyyy-MM-dd.'), 'Incorrect error message');
        
        Test.stopTest();
    }
    
    @isTest
    static void testDoGetServerError() {
        Test.startTest();
        
        // Set up the request with invalid parameter to force exception
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?sinceDate=2023-01-01'; // Use a valid date format
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call method - exception will be triggered by the deliberate error
        GetUpdatedRecordsByDate.doGet();

        // Verify error response
        System.assertEquals(500, res.statusCode, 'Expected 500 Internal Server Error');
        System.assert(res.responseBody.toString().contains('Divide by 0'), 'Incorrect error message'); // Check for the specific exception
        Test.stopTest();
    }

    @isTest
    static void testDoGetNoResults() {
        // Use a future date that won't match any records
        Test.startTest();

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?sinceDate=2099-01-01';
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        GetUpdatedRecordsByDate.doGet();

        // Verify empty results structure
        System.assertEquals(200, res.statusCode, 'Expected 200 OK for no results');
        String responseBody = res.responseBody.toString();
        System.assertEquals('{"Accounts":[],"Contacts":[],"Leads":[]}', responseBody, 'Incorrect empty JSON response');

        Test.stopTest();
    }
    
     @isTest
    static void testDoGetValidDate() {
        // Create test data
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        
        Contact con = new Contact(LastName = 'Test Contact', AccountId = acc.Id);
        insert con;
        
        Lead lead = new Lead(LastName = 'Test Lead', Company = 'Test Company');
        insert lead;
        
        Test.startTest();
        //Update the LastModifiedDate to be in the date range
        // acc.LastModifiedDate = DateTime.newInstance(2025,2,1);
        // update acc;
        // con.LastModifiedDate = DateTime.newInstance(2025,2,1);
        // update con;
        // lead.LastModifiedDate = DateTime.newInstance(2025,2,1);
        // update lead;
        
        // Set up the request
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/getUpdatedRecordsByDate?sinceDate=2025-02-01';
        req.httpMethod = 'GET';
        RestContext.request = req;
        
        // Set up the response
        RestResponse res = new RestResponse();
        RestContext.response = res;
        
        // Call the method
        GetUpdatedRecordsByDate.doGet();
        
        // Verify the response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('"Accounts":['));
        System.assert(responseBody.contains('"Contacts":['));
        System.assert(responseBody.contains('"Leads":['));
        
        Test.stopTest();
    }
}
