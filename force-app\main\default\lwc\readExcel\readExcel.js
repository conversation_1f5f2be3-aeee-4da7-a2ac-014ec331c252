import { LightningElement, track, wire } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { getRecord } from "lightning/uiRecordApi";
import NAME_FIELD from "@salesforce/schema/Account.Name";
import { loadScript } from "lightning/platformResourceLoader";
import xlsx from "@salesforce/resourceUrl/xlsx";
import Display from "@salesforce/apex/ShowJsonData.Display";
import Id from "@salesforce/schema/Account.Id";

let XLS = {};
export default class readExcel extends LightningElement {
  accountId = Id;
  @wire(getRecord, {
    recordId: "$userId",
    fields: [NAME_FIELD]
  })
  record;
  @track acceptedFormats = [".xls", ".xlsx"];
  @track finalData;
  connectedCallback() {
    Promise.all([loadScript(this, xlsx + "/xlsx/xlsx.full.min.js")]).then(
      () => {
        XLS = XLSX;
        console.log(this.record);
        //this.readFromFile();
      }
    );
  }

  handleUploadFinished(event) {
    const uploadedFiles = event.detail.files;
    if (uploadedFiles.length > 0) {
      this.ExcelToJSON(uploadedFiles[0]);
    }
  }

  ExcelToJSON(file) {
    var reader = new FileReader();
    reader.onload = (event) => {
      var data = event.target.result;
      var workbook = XLS.read(data, {
        type: "binary"
      });
      var js = XLS.utils.sheet_to_json(workbook.Sheets.Sheet1);
      const dta = js.map((m) => {
        m.header = m.header + "_new";
        return m;
      });

      this.displayData(JSON.stringify(dta));
    };

    reader.readAsBinaryString(file);
  }

  showToast(msg) {
    const event = new ShowToastEvent({
      variant: "success",
      title: "Upload was successful",
      message: msg
    });
    this.dispatchEvent(event);
  }

  async displayData(jsonData) {
    console.log(jsonData);
    const result = await Display({ data: jsonData });
    this.showToast(result);
    this.finalData = result;
    console.log(this.finalData);
  }
}