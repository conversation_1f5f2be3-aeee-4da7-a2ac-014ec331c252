import { LightningElement, api, track } from "lwc";
import fetchFiles from "@salesforce/apex/FileUploadCtrl.fetchFiles";

export default class ProcessCsv extends LightningElement {
  @api myRecordId;
  @track lstAllFiles = [];

  get acceptedFormats() {
    return [".pdf", ".png"];
  }
  handleUploadFinished(event) {
    // Get the list of uploaded files
    const uploadedFiles = event.detail.files;
    console.log(uploadedFiles);
  }
  connectedCallback() {
    fetchFiles({ recordId: this.myRecordId })
      .then((result) => {
        this.lstAllFiles = result;
      })
      .catch((error) => {
        console.log(error);
        this.lstAllFiles = [];
      });
  }
}