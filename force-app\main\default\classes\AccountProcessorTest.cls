@isTest
public class AccountProcessorTest {
    
    @isTest
    public static void TestAccountProcessorContactsCount(){
        
        //Load Test Data
        List<Account> acc = new List<Account>();
        
        for(integer i=0;i<=300;i++){
            acc.add(new Account(Name='Account '+i));
        }
        insert acc;
        
        List<Contact> lstCon = new List<Contact>();
        List<Id> accIds= new List<Id>();
        for(Account a :acc){
            lstCon.add(new Contact(FirstName='Contact '+a.Name, LastName='Last '+a.Name, AccountId= a.Id));
            accIds.add(a.Id);
        }
        insert lstCon;
        
        //Perform Test
        Test.startTest();
        AccountProcessor.countContacts(accIds);
        Test.stopTest();
        
        // Check Results
        List<Account> accounts = [select id, Number_Of_Contacts__c from Account];
        for(Account a: accounts){
            System.assertEquals(1, a.Number_Of_Contacts__c, 'Error: Atleast one account records is missing the contact');
        }
        
    }
}