public with sharing class DemoStringUtilities {

    // Method takes input list of strings and returns a comma-separated string
    @InvocableMethod(label='Transform String and Collection ' description='Transforms collection to string and vice versa')
    public static List<ResultString> transformStringCollection(List<RequestString> inputStrings) {
        List<ResultString> result = new List<ResultString>();
        for(RequestString req: inputStrings ){
               result.add(transform(req));
        }
        return result;
    }

    // Transform collection of strings to a comma separated string and comma separated string to a collection.
    private static ResultString transform(RequestString requestString){
         ResultString str = new ResultString();
         if( String.isNotBlank(requestString.inputString) && requestString.inputString.contains(',')){
            str.resultList = requestString.inputString.split(',');
        }
         
        if( requestString.inputCollection != null && requestString.inputCollection.size()> 0){
            String outputString ='';
            for(String s : requestString.inputCollection){
                outputString += s +',';
            }
            str.resultString = outputString.removeEnd(',');
        }
         return str;
    }

    
    public class RequestString {
        @InvocableVariable(label='Comma separated String' description='Comma separated string')
        public String inputString;

        @invocableVariable(label='Collection of strings' description='Collection of strings')
        public List<String> inputCollection;
    
    }

    public class ResultString {
        @InvocableVariable
        public String resultString;

        @InvocableVariable
        public List<String> resultList;
    }
}
