public with sharing class Job<PERSON><PERSON>roller {

    @AuraEnabled(cacheable=true)
    public static List<Account> getPartnerHierarchy(String recordId) {
        return [Select Id, Name, 
                    (Select Support_Worker__r.Name, 
                        (Select Name, Time_Period__r.Name,  CreatedDate, Status__c, Total_Hours__c,
                            (Select Name, Date__c, Shift_1__c, Shift_2__c, Total_Hours__c from Timesheet_entries__r ORDER BY Date__c DESC) 
                        from timesheets__r ORDER BY CreatedDate DESC) 
                    from PartnerJobs__r) 
                from Account where id =:recordId];
    

}
}