import { LightningElement, wire } from 'lwc';
import LightningConfirm from 'lightning/confirm';
import getRecordTypes from '@salesforce/apex/MyApexClass.getRecordTypes';
export default class MyComponent extends LightningElement {
    async handleConfirmClick() {
        const result = await LightningConfirm.open({
            message: 'this is the prompt message',
            variant: 'headerless',
            label: 'this is the aria-label value',
            // setting theme would have no effect
        });
        //Confirm has been closed
        //result is true if OK was clicked
        //and false if cancel was clicked
    }

    greeting = 'hello there!!';
    @wire(getRecordTypes)
    recordTypes;

    connectedCallback() {
        console.log('record types are :' + this.recordTypes);
    }

    handleClick(event) {
        console.log(this.recordTypes);
    }
}