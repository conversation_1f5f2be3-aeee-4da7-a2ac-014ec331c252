@RestResource(urlMapping='/Accounts/*/contacts')
global with sharing class AccountManager {

    @HttpGet
    global static Account getAccount(){
        RestRequest request = RestContext.request;
        string accountId = request.requestURI.subStringBetween('Accounts/','/contacts');
        Account result = [select ID, Name, (Select ID, FirstName, LastName from Contacts) from Account where Id = :accountId];
        return result;
    }
}