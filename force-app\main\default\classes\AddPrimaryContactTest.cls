@isTest
public class AddPrimaryContactTest {
    
    @isTest
    private static void AddContactTest(){
        // Load Test data
        List<Account> lstAccount = new List<Account>();
        for(Integer i=0;i<500;i++){
            Account acc= new Account(Name='Test Account');
            acc.BillingState= i<250? 'CA': 'NY';
            lstAccount.add(acc);
        }
        insert lstAccount;
        
        Contact contact = new Contact(FirstName='firstName', LastName='LastName');
        insert contact;
        
        //Perform Test
        Test.startTest();
        AddPrimaryContact con = new AddPrimaryContact(contact, 'CA');
        Id jobId= System.enqueueJob(con);
        Test.stopTest();
        
        //verify the result
        List<Contact> contacts = [select Id  from Contact where Contact.Account.BillingState = 'CA'];
        System.assertEquals(200, contacts.size());
    }
}