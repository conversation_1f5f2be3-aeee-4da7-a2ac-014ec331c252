@isTest
public class DemoStringUtilitiesTest {

    @isTest
    static void testTransformStringCollectionToList() {
        // Test case where inputString is a comma-separated string
        DemoStringUtilities.RequestString req = new DemoStringUtilities.RequestString();
        req.inputString = 'one,two,three';
        req.inputCollection = new List<String>();

        List<DemoStringUtilities.RequestString> reqList = new List<DemoStringUtilities.RequestString>();
        reqList.add(req);

        Test.startTest();
        List<DemoStringUtilities.ResultString> result = DemoStringUtilities.transformStringCollection(reqList);
        Test.stopTest();

        System.assertEquals(1, result.size());
        System.assertEquals(3, result[0].resultList.size());
        System.assertEquals('one', result[0].resultList[0]);
        System.assertEquals('two', result[0].resultList[1]);
        System.assertEquals('three', result[0].resultList[2]);
    }

    @isTest
    static void testListTotransformStringCollection() {
        // Test case where inputCollection is a list of strings
        DemoStringUtilities.RequestString req = new DemoStringUtilities.RequestString();
        req.inputString = '';
        req.inputCollection = new List<String>{'one', 'two', 'three'};

        List<DemoStringUtilities.RequestString> reqList = new List<DemoStringUtilities.RequestString>();
        reqList.add(req);

        Test.startTest();
        List<DemoStringUtilities.ResultString> result = DemoStringUtilities.transformStringCollection(reqList);
        Test.stopTest();

        System.assertEquals(1, result.size());
        System.assertEquals('one,two,three', result[0].resultString);
    }

    @isTest
    static void testBothFieldsPopulated() {
        // Test case where both inputString and inputCollection are populated
        DemoStringUtilities.RequestString req = new DemoStringUtilities.RequestString();
        req.inputString = 'one,two,three';
        req.inputCollection = new List<String>{'four', 'five', 'six'};

        List<DemoStringUtilities.RequestString> reqList = new List<DemoStringUtilities.RequestString>();
        reqList.add(req);

        Test.startTest();
        List<DemoStringUtilities.ResultString> result = DemoStringUtilities.transformStringCollection(reqList);
        Test.stopTest();

        System.assertEquals(1, result.size());
        System.assertEquals(3, result[0].resultList.size());
        System.assertEquals('one', result[0].resultList[0]);
        System.assertEquals('two', result[0].resultList[1]);
        System.assertEquals('three', result[0].resultList[2]);
        System.assertEquals('four,five,six', result[0].resultString);
    }

    @isTest
    static void testEmptyFields() {
        // Test case where both inputString and inputCollection are empty
        DemoStringUtilities.RequestString req = new DemoStringUtilities.RequestString();
        req.inputString = '';
        req.inputCollection = new List<String>();

        List<DemoStringUtilities.RequestString> reqList = new List<DemoStringUtilities.RequestString>();
        reqList.add(req);

        Test.startTest();
        List<DemoStringUtilities.ResultString> result = DemoStringUtilities.transformStringCollection(reqList);
        Test.stopTest();

        System.assertEquals(1, result.size());
        System.assertEquals(null, result[0].resultList);
        System.assertEquals(null, result[0].resultString);

        
    }

    @isTest
    static void testNullFields() {
        // Test case where both inputString and inputCollection are null
        DemoStringUtilities.RequestString req = new DemoStringUtilities.RequestString();
        req.inputString = null;
        req.inputCollection = null;

        List<DemoStringUtilities.RequestString> reqList = new List<DemoStringUtilities.RequestString>();
        reqList.add(req);

        Test.startTest();
        List<DemoStringUtilities.ResultString> result = DemoStringUtilities.transformStringCollection(reqList);
        Test.stopTest();

        System.assertEquals(1, result.size());
        System.assertEquals(null,result[0].resultList);
        System.assertEquals(null,result[0].resultString);
    }
}
