<template>
    <div class="activity-timeline-container">
        <!-- Tabs (Simplified - only Activity is active) -->
        <div class="timeline-tabs">
            <div class="timeline-tab active">Activity</div>
            <div class="timeline-tab">Chatter</div>
        </div>

        <!-- Filter and Action Bar -->
        <div class="timeline-filter-bar">
            <div class="timeline-icons">
                <button class="timeline-icon-button active">
                    <lightning-icon icon-name="utility:task" alternative-text="Tasks" size="x-small"></lightning-icon>
                    <lightning-icon icon-name="utility:chevrondown" alternative-text="Dropdown" size="xx-small"></lightning-icon>
                </button>
                <button class="timeline-icon-button">
                    <lightning-icon icon-name="utility:call" alternative-text="Calls" size="x-small"></lightning-icon>
                    <lightning-icon icon-name="utility:chevrondown" alternative-text="Dropdown" size="xx-small"></lightning-icon>
                </button>
                <button class="timeline-icon-button">
                    <lightning-icon icon-name="utility:event" alternative-text="Events" size="x-small"></lightning-icon>
                    <lightning-icon icon-name="utility:chevrondown" alternative-text="Dropdown" size="xx-small"></lightning-icon>
                </button>
                <button class="timeline-icon-button">
                    <lightning-icon icon-name="utility:email" alternative-text="Emails" size="x-small"></lightning-icon>
                    <lightning-icon icon-name="utility:chevrondown" alternative-text="Dropdown" size="xx-small"></lightning-icon>
                </button>
            </div>
            <div class="timeline-filter-text">
                Filters: All time • All activities • All types
            </div>
            <div class="timeline-actions">
                <a href="#" onclick={handleRefresh}>Refresh</a> •
                <a href="#" onclick={handleExpandAll}>Expand All</a> •
                <a href="#" onclick={handleViewAll}>View All</a>
            </div>
            <button class="timeline-settings-button">
                 <lightning-icon icon-name="utility:settings" alternative-text="Settings" size="x-small"></lightning-icon>
            </button>
        </div>

        <!-- Upcoming & Overdue Section -->
        <div class="timeline-section-header">
            <lightning-icon icon-name="utility:chevrondown" alternative-text="Collapse" size="x-small"></lightning-icon>
            <span class="slds-m-left_xx-small">Upcoming & Overdue</span>
        </div>

        <!-- Timeline Items -->
        <div class="timeline-items-container">
            <template for:each={timelineItems} for:item="item" for:index="index">
                <div key={item.id} class="timeline-item">
                    <!-- Left Side: Arrow, Icon, and Vertical Line -->
                    <div class="timeline-left">
                        <lightning-icon icon-name="utility:chevronright" alternative-text="Expand" size="x-small"></lightning-icon>
                        <div class="timeline-icon-wrapper">
                             <lightning-icon icon-name="utility:task" alternative-text="Task" size="x-small"></lightning-icon>
                        </div>
                        <!-- Vertical line - hide for the last item -->
                        <template if:false={item.isLast}>
                            <div class="timeline-vertical-line"></div>
                        </template>
                    </div>

                    <!-- Right Side: Content -->
                    <div class="timeline-right">
                        <div class="timeline-item-header">
                            <input type="checkbox" class="slds-checkbox_faux" checked={item.isCompleted} disabled>
                            <a href="#" class="timeline-item-subject">{item.subject}</a>
                            <span class="timeline-item-duedate">{item.dueDate}</span>
                            <button class="timeline-item-dropdown">
                                <lightning-icon icon-name="utility:chevrondown" alternative-text="Actions" size="xx-small"></lightning-icon>
                            </button>
                        </div>
                        <div class="timeline-item-description">
                            {item.description}
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- View More Button -->
        <div class="timeline-view-more">
            <button class="slds-button slds-button_neutral" onclick={handleViewMore}>View More</button>
        </div>

        <!-- No Past Activity Message (Conditional) -->
        <template if:false={hasPastActivity}>
             <div class="timeline-no-past-activity slds-text-align_center slds-text-color_weak slds-p-vertical_medium">
                 No past activity. Past meetings and tasks marked as done show up here.
             </div>
        </template>

    </div>
</template>