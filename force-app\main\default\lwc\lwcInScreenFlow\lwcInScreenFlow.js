import { LightningElement,api,track } from 'lwc';

export default class LwcInScreenFlow extends LightningElement {
    @api show = false;
    flowOutput;
    @track inputVars = 
     [
        {
            name: 'commaString',
            type: 'String',
            isCollection: false,
            value: 'Test1,Test2,Test3'
            },
        {
            name: 'AccountIds',
            type: 'String',
            isCollection: true,
            value: 'AccId1,AccId2,AccId3'
        }     
        ];
    
    handleChange(event) {
        this.inputVars.map((a) => {
            if (a.name == 'AccountIds') {
                a.value = event.target.value;
            }
        })

        // for (let i = 0; i < this.inputVars.length; i++) {
        //     this.inputVars[i].value = event.target.value;
            
        // }
        
        this.show = true;

    }
    
    handleStatusChange(event) {
        console.log(JSON.stringify(event.detail));

    }
    

}
    
