/* Calendar Container */
.calendar-container {
    border: 1px solid var(--lwc-colorBorder, #d8dde6);
    border-radius: var(--lwc-borderRadiusMedium, 0.25rem);
    background-color: var(--lwc-colorBackground, #ffffff);
}

/* Calendar Header */
.calendar-header {
    background-color: var(--lwc-colorBackgroundAlt, #f3f2f2);
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
}

.calendar-day-header {
    flex: 1;
    font-weight: 600;
    color: var(--lwc-colorTextDefault, #181818);
    border-right: 1px solid var(--lwc-colorBorder, #d8dde6);
}

.calendar-day-header:last-child {
    border-right: none;
}

/* Calendar Body */
.calendar-body {
    min-height: 400px;
}

.calendar-week {
    border-bottom: 1px solid var(--lwc-colorBorder, #d8dde6);
}

.calendar-week:last-child {
    border-bottom: none;
}

/* Calendar Day */
.calendar-day {
    flex: 1;
    min-height: 100px;
    border-right: 1px solid var(--lwc-colorBorder, #d8dde6);
    padding: 0.5rem;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.calendar-day:last-child {
    border-right: none;
}

.calendar-day:hover {
    background-color: var(--lwc-colorBackgroundRowHover, #f3f2f2);
}

.calendar-day.today {
    background-color: var(--lwc-colorBackgroundBrandPrimary, #0176d3);
    color: white;
}

.calendar-day.other-month {
    background-color: var(--lwc-colorBackgroundAlt2, #fafaf9);
    color: var(--lwc-colorTextWeak, #706e6b);
}

.calendar-day.selected {
    background-color: var(--lwc-colorBackgroundSelection, #ecebea);
    border: 2px solid var(--lwc-colorBorderBrand, #0176d3);
}

/* Day Number */
.day-number {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* Day Events */
.day-events {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.event-dot {
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    line-height: 1;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.event-dot:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.event-title {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Event Type Colors */
.event-meeting {
    background-color: #0176d3;
    color: white;
}

.event-deadline {
    background-color: #c23934;
    color: white;
}

.event-presentation {
    background-color: #ff6b35;
    color: white;
}

.event-training {
    background-color: #00a86b;
    color: white;
}

.event-review {
    background-color: #8b5cf6;
    color: white;
}

.event-default {
    background-color: var(--lwc-colorBackgroundAlt, #f3f2f2);
    color: var(--lwc-colorTextDefault, #181818);
    border: 1px solid var(--lwc-colorBorder, #d8dde6);
}

/* List View */
.list-container {
    max-height: 600px;
    overflow-y: auto;
}

.event-item {
    transition: all 0.2s ease;
}

.event-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.event-details h3 {
    margin-bottom: 0.25rem;
}

.event-meta {
    gap: 0.5rem;
}

/* Priority Classes */
.priority-high {
    background-color: #c23934;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-medium {
    background-color: #ff6b35;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-low {
    background-color: #00a86b;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Navigation */
.calendar-navigation {
    align-items: center;
}

.view-toggle {
    display: flex;
    align-items: center;
}

/* Modal Overrides */
.slds-modal__container {
    max-width: 600px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-day {
        min-height: 80px;
        padding: 0.25rem;
    }
    
    .day-number {
        font-size: 0.75rem;
    }
    
    .event-dot {
        font-size: 0.625rem;
        padding: 0.0625rem 0.125rem;
    }
    
    .calendar-navigation {
        flex-direction: column;
        gap: 1rem;
    }
    
    .view-toggle {
        width: 100%;
        justify-content: center;
    }
}

/* Loading State */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--lwc-colorTextWeak, #706e6b);
}

.empty-state lightning-icon {
    opacity: 0.5;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.event-item {
    animation: fadeIn 0.3s ease-out;
}

/* Focus States */
.calendar-day:focus {
    outline: 2px solid var(--lwc-colorBorderBrand, #0176d3);
    outline-offset: -2px;
}

.event-dot:focus {
    outline: 2px solid var(--lwc-colorBorderBrand, #0176d3);
    outline-offset: 1px;
}
