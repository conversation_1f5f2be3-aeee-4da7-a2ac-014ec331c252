@isTest 
private class AccountManagerTest {
	@isTest
    static void testGetAccount(){
        Account a = new Account(Name='TestAccount');
        insert a;
        Contact c = new Contact(FirstName='first', LastName='LastName', AccountId= a.Id);
        insert c;
        
        RestRequest request = new RestRequest();
        request.requestURI = 'https://d2w00000fahq3eab-dev-ed.my.salesforce.com/services/apexrest/Accounts/'+a.id+'/contacts';
        request.httpMethod ='GET';
        RestContext.request = request;
        
        Account acc = AccountManager.getAccount();
        
        System.assert(acc !=null);
        System.assertEquals('TestAccount', acc.Name);
        System.assertEquals(1, acc.contacts.size());
    }
}